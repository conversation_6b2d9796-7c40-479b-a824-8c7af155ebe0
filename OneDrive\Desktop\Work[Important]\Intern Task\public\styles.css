* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: white;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.header h1 {
    font-size: 1.8rem;
    margin-bottom: 8px;
    font-weight: 600;
}

.header p {
    opacity: 0.9;
    font-size: 0.95rem;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8fafc;
}

.message {
    margin-bottom: 20px;
    animation: fadeIn 0.3s ease-in;
}

.message-content {
    max-width: 85%;
}

.bot-message .message-content {
    margin-left: 0;
    margin-right: auto;
}

.user-message .message-content {
    margin-left: auto;
    margin-right: 0;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.85rem;
    color: #64748b;
}

.sender {
    font-weight: 600;
}

.message-text {
    background: white;
    padding: 15px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    line-height: 1.5;
}

.user-message .message-text {
    background: #4f46e5;
    color: white;
}

.task-analysis {
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 12px;
    padding: 20px;
    margin-top: 15px;
}

.analysis-section {
    margin-bottom: 15px;
}

.analysis-section:last-child {
    margin-bottom: 0;
}

.analysis-label {
    font-weight: 600;
    color: #0f172a;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.analysis-value {
    color: #334155;
}

.priority-high { color: #dc2626; font-weight: 600; }
.priority-medium { color: #ea580c; font-weight: 600; }
.priority-low { color: #16a34a; font-weight: 600; }

.effort-large { color: #dc2626; font-weight: 600; }
.effort-medium { color: #ea580c; font-weight: 600; }
.effort-small { color: #16a34a; font-weight: 600; }

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 5px;
}

.tag {
    background: #4f46e5;
    color: white;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e2e8f0;
}

.task-form {
    width: 100%;
}

.input-group {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

#taskInput {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-family: inherit;
    font-size: 0.95rem;
    resize: vertical;
    min-height: 60px;
    transition: border-color 0.2s ease;
}

#taskInput:focus {
    outline: none;
    border-color: #4f46e5;
}

.submit-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
    height: fit-content;
}

.submit-btn:hover:not(:disabled) {
    background: #4338ca;
    transform: translateY(-1px);
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.spinner {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

.status-bar {
    padding: 10px 20px;
    background: #f1f5f9;
    border-top: 1px solid #e2e8f0;
    font-size: 0.85rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #16a34a;
}

.status-dot.error {
    background: #dc2626;
}

.status-dot.processing {
    background: #ea580c;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@media (max-width: 768px) {
    .container {
        height: 100vh;
        margin: 0;
    }
    
    .input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .submit-btn {
        width: 100%;
    }
    
    .message-content {
        max-width: 95%;
    }
}
