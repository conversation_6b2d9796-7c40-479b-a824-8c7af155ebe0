{"name": "smart-task-summarizer", "version": "1.0.0", "description": "A tool for summarizing and tagging task descriptions using Gemini 2.0 Flash", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "keywords": ["task", "summarizer", "ai", "gemini", "tagging"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "@google/generative-ai": "^0.2.1"}, "devDependencies": {"nodemon": "^3.0.2"}}