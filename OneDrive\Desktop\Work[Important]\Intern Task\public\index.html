<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Task Summarizer + Tagger</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🤖 Smart Task Summarizer + Tagger</h1>
            <p>Paste your raw task descriptions and get intelligent summaries with tags!</p>
        </header>

        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="message bot-message">
                    <div class="message-content">
                        <div class="message-header">
                            <span class="sender">🤖 AI Assistant</span>
                            <span class="timestamp"></span>
                        </div>
                        <div class="message-text">
                            Welcome! I'm here to help you summarize and tag your tasks. Just paste or type your task description below and I'll analyze it for you.
                        </div>
                    </div>
                </div>
            </div>

            <div class="input-container">
                <form id="taskForm" class="task-form">
                    <div class="input-group">
                        <textarea 
                            id="taskInput" 
                            placeholder="Paste your task description here... (e.g., 'Need to prepare quarterly report by Friday, schedule team meeting, and review budget proposals')"
                            rows="3"
                            required
                        ></textarea>
                        <button type="submit" id="submitBtn" class="submit-btn">
                            <span class="btn-text">Analyze Task</span>
                            <span class="btn-loading" style="display: none;">
                                <span class="spinner"></span>
                                Processing...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="status-bar">
            <span id="statusIndicator" class="status-indicator">
                <span class="status-dot"></span>
                <span class="status-text">Ready</span>
            </span>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
