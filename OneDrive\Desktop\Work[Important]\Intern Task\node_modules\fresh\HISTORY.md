0.5.2 / 2017-09-13
==================

  * Fix regression matching multiple ETags in `If-None-Match`
  * perf: improve `If-None-Match` token parsing

0.5.1 / 2017-09-11
==================

  * Fix handling of modified headers with invalid dates
  * perf: improve ETag match loop

0.5.0 / 2017-02-21
==================

  * Fix incorrect result when `If-None-Match` has both `*` and ETags
  * Fix weak `ETag` matching to match spec
  * perf: delay reading header values until needed
  * perf: skip checking modified time if ETag check failed
  * perf: skip parsing `If-None-Match` when no `ETag` header
  * perf: use `Date.parse` instead of `new Date`

0.4.0 / 2017-02-05
==================

  * Fix false detection of `no-cache` request directive
  * perf: enable strict mode
  * perf: hoist regular expressions
  * perf: remove duplicate conditional
  * perf: remove unnecessary boolean coercions

0.3.0 / 2015-05-12
==================

  * Add weak `ETag` matching support

0.2.4 / 2014-09-07
==================

  * Support Node.js 0.6

0.2.3 / 2014-09-07
==================

  * Move repository to jshttp

0.2.2 / 2014-02-19
==================

  * Revert "Fix for blank page on Safari reload"

0.2.1 / 2014-01-29
==================

  * Fix for blank page on Safari reload

0.2.0 / 2013-08-11
==================

  * Return stale for `Cache-Control: no-cache`

0.1.0 / 2012-06-15
==================

  * Add `If-None-Match: *` support

0.0.1 / 2012-06-10
==================

  * Initial release
