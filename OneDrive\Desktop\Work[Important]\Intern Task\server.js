const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const { GoogleGenerativeAI } = require('@google/generative-ai');

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Task processing endpoint
app.post('/api/process-task', async (req, res) => {
    try {
        const { taskDescription } = req.body;
        
        if (!taskDescription || taskDescription.trim() === '') {
            return res.status(400).json({ 
                error: 'Task description is required' 
            });
        }

        // Get the generative model
        const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

        // Create the prompt for task summarization and tagging
        const prompt = `
        You are a smart task analyzer for a busy project manager. Analyze the following task description and provide:

        1. A concise summary (1-2 sentences max)
        2. Priority level (High, Medium, Low) based on urgency indicators
        3. Estimated effort (Small, Medium, Large) based on complexity
        4. Relevant tags (3-5 tags max, use categories like: urgent, meeting, development, research, admin, communication, planning, review, etc.)
        5. Suggested deadline (if any time indicators are present)

        Task Description: "${taskDescription}"

        Please respond in the following JSON format:
        {
            "summary": "Brief summary here",
            "priority": "High/Medium/Low",
            "effort": "Small/Medium/Large", 
            "tags": ["tag1", "tag2", "tag3"],
            "suggestedDeadline": "deadline or null",
            "originalTask": "original task description"
        }
        `;

        // Generate content
        const result = await model.generateContent(prompt);
        const response = await result.response;
        const text = response.text();

        // Try to parse the JSON response
        let parsedResponse;
        try {
            // Extract JSON from the response (in case there's extra text)
            const jsonMatch = text.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                parsedResponse = JSON.parse(jsonMatch[0]);
            } else {
                throw new Error('No JSON found in response');
            }
        } catch (parseError) {
            // Fallback if JSON parsing fails
            parsedResponse = {
                summary: "Task analysis completed",
                priority: "Medium",
                effort: "Medium",
                tags: ["general"],
                suggestedDeadline: null,
                originalTask: taskDescription,
                rawResponse: text
            };
        }

        res.json({
            success: true,
            data: parsedResponse
        });

    } catch (error) {
        console.error('Error processing task:', error);
        res.status(500).json({ 
            error: 'Failed to process task',
            details: error.message 
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        geminiConfigured: !!process.env.GEMINI_API_KEY
    });
});

app.listen(PORT, () => {
    console.log(`🚀 Smart Task Summarizer server running on http://localhost:${PORT}`);
    console.log(`📝 Gemini API configured: ${!!process.env.GEMINI_API_KEY}`);
});
